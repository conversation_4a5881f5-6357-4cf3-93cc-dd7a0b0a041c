// HTTP API 测试脚本
// 确保 NestJS 服务器正在运行 (npm run start:dev)
// 运行命令: node test-auth-api.js

const http = require('http');

// 发送 HTTP 请求的辅助函数
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: jsonBody
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// 测试登录接口
async function testLogin(username, password, testName) {
  console.log(`\n📋 ${testName}`);
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  };

  const loginData = { username, password };

  try {
    const response = await makeRequest(options, loginData);
    console.log(`状态码: ${response.statusCode}`);
    
    if (response.statusCode === 200) {
      console.log('✅ 登录成功');
      console.log('用户信息:', response.body.user);
      console.log('Token 类型:', response.body.token_type);
      console.log('过期时间:', response.body.expires_in, '秒');
      return response.body.access_token;
    } else {
      console.log('❌ 登录失败');
      console.log('错误信息:', response.body);
      return null;
    }
  } catch (error) {
    console.log('❌ 请求失败:', error.message);
    return null;
  }
}

// 测试受保护的路由
async function testProtectedRoute(token, testName) {
  console.log(`\n📋 ${testName}`);
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/auth/profile',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
    }
  };

  try {
    const response = await makeRequest(options);
    console.log(`状态码: ${response.statusCode}`);
    
    if (response.statusCode === 200) {
      console.log('✅ 访问受保护路由成功');
      console.log('响应:', response.body);
    } else {
      console.log('❌ 访问受保护路由失败');
      console.log('错误信息:', response.body);
    }
  } catch (error) {
    console.log('❌ 请求失败:', error.message);
  }
}

// 检查服务器是否运行
async function checkServer() {
  console.log('🔍 检查服务器是否运行...');
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/',
    method: 'GET',
  };

  try {
    const response = await makeRequest(options);
    if (response.statusCode === 200) {
      console.log('✅ 服务器正在运行');
      return true;
    } else {
      console.log('❌ 服务器响应异常');
      return false;
    }
  } catch (error) {
    console.log('❌ 无法连接到服务器');
    console.log('请确保运行了: npm run start:dev');
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试 Auth API...');
  
  // 检查服务器
  const serverRunning = await checkServer();
  if (!serverRunning) {
    console.log('\n💡 请先启动 NestJS 服务器:');
    console.log('   npm run start:dev');
    return;
  }

  // 测试正确登录
  const token1 = await testLogin('admin', 'password123', '测试1: 正确的用户名和密码');
  
  // 测试错误密码
  await testLogin('admin', 'wrongpassword', '测试2: 错误的密码');
  
  // 测试不存在的用户
  await testLogin('nonexistent', 'password123', '测试3: 不存在的用户');
  
  // 测试第二个用户
  const token2 = await testLogin('user1', 'password123', '测试4: 第二个用户登录');
  
  // 测试受保护路由
  if (token1) {
    await testProtectedRoute(token1, '测试5: 使用有效 token 访问受保护路由');
  }
  
  // 测试无效 token
  await testProtectedRoute('invalid-token', '测试6: 使用无效 token 访问受保护路由');

  console.log('\n🎉 API 测试完成！');
}

// 运行测试
runTests().catch(console.error);
