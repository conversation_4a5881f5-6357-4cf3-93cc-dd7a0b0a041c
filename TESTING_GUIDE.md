# Auth 模块测试指南

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install @nestjs/jwt @nestjs/passport passport passport-jwt bcryptjs class-validator class-transformer
npm install --save-dev @types/bcryptjs @types/passport-jwt
```

### 2. 测试方法

我为你准备了三种测试方法：

## 方法一：逻辑测试（无需启动服务器）

运行简化的逻辑测试：

```bash
node test-auth-simple.js
```

这个测试会验证：
- ✅ 用户验证逻辑
- ✅ 密码加密和比较
- ✅ JWT token 生成
- ✅ 错误处理

## 方法二：API 接口测试（需要启动服务器）

1. 启动开发服务器：
```bash
npm run start:dev
```

2. 在另一个终端运行 API 测试：
```bash
node test-auth-api.js
```

这个测试会验证：
- ✅ POST /auth/login 接口
- ✅ GET /auth/profile 受保护路由
- ✅ JWT token 验证
- ✅ 错误响应

## 方法三：使用 curl 手动测试

### 1. 启动服务器
```bash
npm run start:dev
```

### 2. 测试登录接口

**成功登录：**
```bash
curl -X POST http://localhost:3000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password123"}'
```

**预期响应：**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "user": {
    "id": "1",
    "username": "admin",
    "tenantId": "tenant-1",
    "email": "<EMAIL>"
  }
}
```

**失败登录（错误密码）：**
```bash
curl -X POST http://localhost:3000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "wrongpassword"}'
```

**预期响应：**
```json
{
  "statusCode": 401,
  "message": "Invalid username or password",
  "error": "Unauthorized"
}
```

### 3. 测试受保护路由

**使用有效 token：**
```bash
curl -X GET http://localhost:3000/auth/profile \
  -H "Authorization: Bearer <your-access-token>"
```

**预期响应：**
```json
{
  "message": "This is a protected route",
  "user": {
    "userId": "1",
    "username": "admin",
    "tenantId": "tenant-1"
  }
}
```

**使用无效 token：**
```bash
curl -X GET http://localhost:3000/auth/profile \
  -H "Authorization: Bearer invalid-token"
```

**预期响应：**
```json
{
  "statusCode": 401,
  "message": "Unauthorized"
}
```

## 方法四：单元测试

运行 Jest 单元测试：

```bash
npm run test -- auth.controller.spec.ts
```

## 🧪 测试用户账号

| 用户名 | 密码 | Tenant ID | 说明 |
|--------|------|-----------|------|
| admin | password123 | tenant-1 | 管理员账号 |
| user1 | password123 | tenant-2 | 普通用户账号 |

## 🔍 验证要点

### 1. 登录接口验证
- ✅ 用户名和密码必填验证
- ✅ 密码最小长度验证（6位）
- ✅ 用户存在性验证
- ✅ 密码正确性验证
- ✅ JWT token 包含正确的 payload

### 2. JWT Payload 验证
确保 JWT token 包含以下字段：
```json
{
  "userId": "1",
  "tenantId": "tenant-1", 
  "username": "admin",
  "iat": 1234567890,
  "exp": 1234571490
}
```

### 3. 错误处理验证
- ✅ 401 Unauthorized for invalid credentials
- ✅ 400 Bad Request for validation errors
- ✅ 401 Unauthorized for invalid/expired tokens

## 🐛 常见问题

### 1. 依赖安装失败
如果 npm 命令不可用，请确保已安装 Node.js 并配置了环境变量。

### 2. 服务器启动失败
检查是否有端口冲突，默认端口是 3000。

### 3. JWT 验证失败
确保在生产环境中设置了 `JWT_SECRET` 环境变量。

## 📝 下一步

测试通过后，你可以：
1. 集成数据库（如 PostgreSQL、MySQL）
2. 添加用户注册功能
3. 实现角色和权限管理
4. 添加刷新 token 机制
5. 集成第三方认证（如 OAuth2）
