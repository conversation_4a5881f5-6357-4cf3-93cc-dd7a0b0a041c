// 简单的测试脚本，用于验证 auth 模块的基本逻辑
// 运行命令: node test-auth-simple.js

const crypto = require('crypto');

// 模拟 bcrypt 功能
function hashPassword(password) {
  return crypto.createHash('sha256').update(password + 'salt').digest('hex');
}

function comparePassword(plainPassword, hashedPassword) {
  const hashedInput = hashPassword(plainPassword);
  return hashedInput === hashedPassword;
}

// 模拟 JWT 功能
function createJWT(payload) {
  const header = { alg: 'HS256', typ: 'JWT' };
  const encodedHeader = Buffer.from(JSON.stringify(header)).toString('base64');
  const encodedPayload = Buffer.from(JSON.stringify(payload)).toString('base64');
  const signature = crypto.createHmac('sha256', 'secret').update(encodedHeader + '.' + encodedPayload).digest('base64');
  return `${encodedHeader}.${encodedPayload}.${signature}`;
}

// 模拟用户数据
const users = [
  {
    id: '1',
    username: 'admin',
    password: hashPassword('password123'),
    tenantId: 'tenant-1',
    email: '<EMAIL>',
  },
  {
    id: '2',
    username: 'user1',
    password: hashPassword('password123'),
    tenantId: 'tenant-2',
    email: '<EMAIL>',
  },
];

// 模拟登录逻辑
function login(username, password) {
  console.log(`\n🔍 尝试登录: ${username}`);
  
  // 查找用户
  const user = users.find(u => u.username === username);
  if (!user) {
    console.log('❌ 用户不存在');
    return { success: false, error: 'User not found' };
  }
  
  // 验证密码
  if (!comparePassword(password, user.password)) {
    console.log('❌ 密码错误');
    return { success: false, error: 'Invalid password' };
  }
  
  // 生成 JWT
  const payload = {
    userId: user.id,
    tenantId: user.tenantId,
    username: user.username,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 3600, // 1小时后过期
  };
  
  const token = createJWT(payload);
  
  console.log('✅ 登录成功');
  return {
    success: true,
    access_token: token,
    token_type: 'Bearer',
    expires_in: 3600,
    user: {
      id: user.id,
      username: user.username,
      tenantId: user.tenantId,
      email: user.email,
    },
  };
}

// 运行测试
console.log('🚀 开始测试 Auth 模块功能...');

// 测试1: 正确的用户名和密码
console.log('\n📋 测试1: 正确的用户名和密码');
const result1 = login('admin', 'password123');
console.log('结果:', result1.success ? '✅ 通过' : '❌ 失败');
if (result1.success) {
  console.log('Token:', result1.access_token.substring(0, 50) + '...');
  console.log('用户信息:', result1.user);
}

// 测试2: 错误的密码
console.log('\n📋 测试2: 错误的密码');
const result2 = login('admin', 'wrongpassword');
console.log('结果:', result2.success ? '❌ 失败' : '✅ 通过');

// 测试3: 不存在的用户
console.log('\n📋 测试3: 不存在的用户');
const result3 = login('nonexistent', 'password123');
console.log('结果:', result3.success ? '❌ 失败' : '✅ 通过');

// 测试4: 第二个用户
console.log('\n📋 测试4: 第二个用户登录');
const result4 = login('user1', 'password123');
console.log('结果:', result4.success ? '✅ 通过' : '❌ 失败');
if (result4.success) {
  console.log('Tenant ID:', result4.user.tenantId);
}

console.log('\n🎉 测试完成！');
console.log('\n💡 如果所有测试都通过，说明 auth 模块的核心逻辑是正确的。');
console.log('💡 接下来你可以安装依赖并启动 NestJS 服务器进行完整测试。');
