# Auth Module

这是一个完整的 NestJS 认证模块，提供 JWT 基础的用户认证功能。

## 功能特性

- ✅ POST /auth/login - 用户登录接口
- ✅ JWT Token 生成和验证
- ✅ 类验证器参数校验
- ✅ 密码加密存储 (bcrypt)
- ✅ JWT payload 包含 userId 和 tenantId
- ✅ 受保护的路由示例
- ✅ 单元测试

## 安装依赖

在使用此模块之前，请安装以下依赖：

```bash
npm install @nestjs/jwt @nestjs/passport passport passport-jwt bcryptjs class-validator class-transformer
npm install --save-dev @types/bcryptjs @types/passport-jwt
```

## API 接口

### 1. 登录接口

**POST** `/auth/login`

**请求体：**
```json
{
  "username": "admin",
  "password": "password123"
}
```

**响应：**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "user": {
    "id": "1",
    "username": "admin",
    "tenantId": "tenant-1",
    "email": "<EMAIL>"
  }
}
```

### 2. 获取用户信息（受保护）

**GET** `/auth/profile`

**Headers：**
```
Authorization: Bearer <access_token>
```

### 3. 验证 Token（受保护）

**POST** `/auth/verify`

**Headers：**
```
Authorization: Bearer <access_token>
```

## 测试用户

模块中预设了以下测试用户：

| 用户名 | 密码 | Tenant ID |
|--------|------|-----------|
| admin | password123 | tenant-1 |
| user1 | password123 | tenant-2 |

## JWT Payload 结构

```typescript
{
  userId: string;
  tenantId: string;
  username: string;
  iat: number;  // 签发时间
  exp: number;  // 过期时间
}
```

## 环境变量

建议在 `.env` 文件中设置以下环境变量：

```env
JWT_SECRET=your-super-secret-jwt-key
```

## 使用示例

### 在其他控制器中使用 JWT 保护

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from './auth/guards/jwt-auth.guard';

@Controller('protected')
export class ProtectedController {
  @UseGuards(JwtAuthGuard)
  @Get()
  getProtectedData(@Request() req) {
    // req.user 包含 JWT payload 信息
    return {
      message: 'This is protected data',
      user: req.user
    };
  }
}
```

## 运行测试

```bash
npm run test -- auth.controller.spec.ts
```
