import { Injectable } from '@nestjs/common';
import { User } from '../interfaces/user.interface';
import * as bcrypt from 'bcryptjs';

@Injectable()
export class UserService {
  // 模拟用户数据，实际项目中应该连接数据库
  private readonly users: User[] = [
    {
      id: '1',
      username: 'admin',
      password: '$2a$10$N9qo8uLOickgx2ZMRZoMye.IjPeGvGzluByJ/iBdgTTn0F5xmtRCK', // 'password123' hashed
      tenantId: 'tenant-1',
      email: '<EMAIL>',
    },
    {
      id: '2',
      username: 'user1',
      password: '$2a$10$N9qo8uLOickgx2ZMRZoMye.IjPeGvGzluByJ/iBdgTTn0F5xmtRCK', // 'password123' hashed
      tenantId: 'tenant-2',
      email: '<EMAIL>',
    },
  ];

  async findByUsername(username: string): Promise<User | undefined> {
    return this.users.find(user => user.username === username);
  }

  async validatePassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  async hashPassword(password: string): Promise<string> {
    const saltRounds = 10;
    return bcrypt.hash(password, saltRounds);
  }
}
