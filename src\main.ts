import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 启用全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true, // 自动删除非装饰器属性
    forbidNonWhitelisted: true, // 如果有非白名单属性则抛出错误
    transform: true, // 自动转换类型
  }));

  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
